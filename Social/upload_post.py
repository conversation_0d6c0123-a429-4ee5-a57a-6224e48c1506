import os
from instagrapi import Client


def load_session(session_file):
    try:
        # Create a client instance
        cl = Client()
        cl.load_settings(session_file)
        if cl.get_timeline_feed():
            print("Session loaded successfully")
        else:
            print("Failed to load session")
        return cl
    except Exception as e:
        print(e)
        return False


def upload_post_images_fun(session_file, media_paths, caption):
    try:
        client = load_session(session_file)
        if client == False:
            return False
        if len(media_paths) > 1:
            print('entered the multiple image')
            upload = client.album_upload(media_paths, caption)
            print(upload)
        else:
            print('entered the single image')
            upload = client.photo_upload(media_paths[0], caption)
            print(upload.id)
        if upload.code:
            return True , upload.id
        else:
            return False
    except Exception as e:
        print(e)
        return False



def upload_insta_video(session_file, media_path, caption, title):
    try:
        client = load_session(session_file)
        if client == False:
            return False
        for upload_insta in media_path:
            upload = client.igtv_upload(upload_insta, title, caption)
            print(upload.id)
        if upload.code:
            return True , upload.id
        else:
            return False
    except Exception as e:
        print(e)
        return False

def get_user_info(email):
    session_file = f'json/instagram/{email}.json'
    client = load_session(session_file)
    account = client.account_info()
    print(account)
    return account
# media_paths = [
#     {"media_type": 1, "image_url": "../media/post_files/2_QcE76Sl.jpg"},
#     {"media_type": 2, "video_url": "../media/post_files/IMG_8983_uWVOvtW_ayLDWXs.MOV"},
#     {"media_type": 1, "image_url": "../media/post_files/Social_Connect_Screen_1.png"},
# ]
# caption = 'Insta multiple image uploading #instagram'
# title = 'title'
# print(upload_post_images_fun(session_file,media_paths,caption))

# track = client.search_music('Tu Hain Toh')
# print(track)
# track_info = client.track_info_by_id('***************')
# print(track_info)
