import razorpay
import json


client = razorpay.Client(auth=("***********************", "Yq7jnUyVrWEoNRorZxYap7CF"))

callback_url = "https://api.flowkar.com/api/payment/callback"

def create_payment_link(user_token,amount, customer_name, customer_email,currency=None):
    link = client.payment_link.create({
    "amount": amount,
    "currency": currency if currency else "INR",
    "description": "Purchase of Flowkar Premium Services",
    "customer": {
        "name": customer_name,
        "email": customer_email
    },
    "notify": {
        "sms": False,
        "email": True
    },
    "notes": {
        "token":user_token
    },
    "callback_url": callback_url,
    "callback_method": "get"
    })
    return link['short_url']



def get_payment_status(payment_id):
    payment = client.payment.fetch(payment_id)
    return payment


# @csrf_exempt
# def RazorpayCallbackView(request):
#     try:
#         # Log the request method and headers
#         logger.info(f"Request Method: {request.method}")
#         logger.info(f"Request Headers: {dict(request.headers)}")
        
#         if request.method == 'GET':
#             # Handle GET request (payment link callback)
#             event_data = {
#                 'razorpay_payment_id': request.GET.get('razorpay_payment_id'),
#                 'razorpay_payment_link_id': request.GET.get('razorpay_payment_link_id'),
#                 'razorpay_payment_link_reference_id': request.GET.get('razorpay_payment_link_reference_id'),
#                 'razorpay_payment_link_status': request.GET.get('razorpay_payment_link_status'),
#                 'razorpay_signature': request.GET.get('razorpay_signature')
#             }
#             logger.info(f"GET Request Data: {event_data}")
#         else:
#             # Handle POST request (webhook)
#             webhook_body = request.body
#             logger.info(f"Raw Webhook Body: {webhook_body}")
            
#             # Try to parse the body as JSON
#             try:
#                 event_data = json.loads(webhook_body)
#                 logger.info(f"Parsed Webhook Data: {event_data}")
#             except json.JSONDecodeError as e:
#                 logger.error(f"Failed to parse webhook body as JSON: {e}")
#                 # If it's not JSON, try to decode as string
#                 event_data = webhook_body.decode('utf-8')
#                 logger.info(f"Decoded Webhook Body: {event_data}")
            
#         return JsonResponse({
#             'status': 'Webhook received',
#             'event': event_data,
#             'request_method': request.method,
#             'content_type': request.content_type
#         }, safe=False)
#     except Exception as e:
#         logger.error(f"Error processing webhook: {str(e)}")
#         return JsonResponse({
#             'status': 'error',
#             'message': str(e),
#             'web_body': f"{str(request.body)}",
#             'request_method': request.method,
#             'content_type': request.content_type
#         }, status=400)