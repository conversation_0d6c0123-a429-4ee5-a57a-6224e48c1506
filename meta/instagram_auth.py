import requests
import secrets
INSTAGRAM_CLIENT_ID = 1579699146279142
# INSTAGRAM_REDIRECT_URI = 'https://staging.yooii.com/api/instagram/'
# INSTAGRAM_REDIRECT_URI = 'https://staging.flowkar.com/api/instagram/'
# INSTAGRAM_REDIRECT_URI = 'https://dev.flowkar.com/api/instagram/'
INSTAGRAM_REDIRECT_URI = 'https://api.flowkar.com/api/instagram/'


def generate_instagram_url(user_id):
    # Generate a state token for security
    state = secrets.token_urlsafe()

    # Construct the Instagram OAuth URL
    auth_url = (
        f"https://instagram.com/oauth/authorize?enable_fb_login=1&force_authentication=1&"
        f"client_id={INSTAGRAM_CLIENT_ID}&"
        f"redirect_uri={INSTAGRAM_REDIRECT_URI}&"
        f"scope=instagram_business_basic,instagram_business_content_publish,instagram_business_manage_messages&"
        f"response_type=code&"
        f"state={user_id}"
    )

    return auth_url

def generate_instagram_url_dev(user_id):
    # Generate a state token for security
    state = secrets.token_urlsafe()

    # Construct the Instagram OAuth URL
    auth_url = (
        f"https://instagram.com/oauth/authorize?enable_fb_login=1&force_authentication=1&"
        f"client_id={INSTAGRAM_CLIENT_ID}&"
        f"redirect_uri={INSTAGRAM_REDIRECT_URI}&"
        f"scope=instagram_business_basic,instagram_business_content_publish,instagram_business_manage_insights,instagram_business_manage_messages&"
        f"response_type=code&"
        f"state={user_id}"
    )

    return auth_url


def get_instagram_user_info(access_token, user_id):
    """
    Fetch the Instagram user's username and profile image.

    :param access_token: The access token obtained after user authorization.
    :param user_id: The Instagram user's ID.
    :return: A dictionary containing username and profile picture URL.
    """
    url = f"https://graph.instagram.com/{user_id}"
    # url = f"https://graph.facebook.com/v21.0/{user_id}"
    params = {
        "fields": "id,username,profile_picture_url,biography,name,followers_count,follows_count,media",
        "access_token": access_token
    }

    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        user_info = response.json()
        # print('user_info',user_info)
        return {
            "username": user_info.get("username"),
            "profile_picture_url": user_info.get("profile_picture_url"),
            "name": user_info.get("name"),
            "followers_count": user_info.get("followers_count"),
            "follows_count": user_info.get("follows_count"),
            "follows_count": user_info.get("follows_count"),
            # "media": user_info.get("media")['data'][0]['id'] if user_info.get("media")['data'] else '',
        }
    except requests.exceptions.RequestException as e:
        print(f"Error fetching user info: {e}")
        return None


# print(get_instagram_user_info('IGQWRNeGxqZAWdHRzlYNThvNzRGdVZAtUmpESWcxSDlkSlphZAjFGNk9FQ19odklkZAnZASM2Y1NlJsS0JQQktPUFdBcHhsYW9yTmdpT1VNU2pGU2tjUzlGeUZAFQ1VmTk5NYzlqU21BeXFzQ2w0UQZDZD','9365775013436815'))
