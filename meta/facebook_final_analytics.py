import json
import requests
from datetime import datetime, timedelta

def get_facebook_page_metrics(page_id, access_token, start_date, end_date):
    url = f"https://graph.facebook.com/{page_id}/insights"
    params = {
        "metric": "page_daily_follows,page_fans,page_impressions,page_impressions_unique,page_views_total",
        "period": "day",
        "since": start_date,
        "until": end_date,
        "access_token": access_token
    }

    page_data = {
        "page_daily_follows": [],
        "page_fans": [],
        "page_impressions": [],
        "page_impressions_unique": [],
        "page_views_total": []
    }

    while url:
        response = requests.get(url, params=params if "?" not in url else {})
        if response.status_code == 200:
            json_response = response.json()
            for entry in json_response.get("data", []):
                metric_name = entry.get("name")
                if metric_name in page_data:
                    if entry.get("period") == "day":
                        page_data[metric_name].extend(entry.get("values", []))
            url = json_response.get("paging", {}).get("next")
        else:
            return False , response.json()['error']['error_user_msg']

    return True,fill_missing_dates_for_metrics(page_data, start_date, end_date)


def fill_missing_dates_for_metrics(page_data, start_date, end_date):
    start_dt = datetime.strptime(start_date, "%Y-%m-%d")
    end_dt = datetime.strptime(end_date, "%Y-%m-%d")

    follows_dict = {entry["end_time"][:10]: entry["value"] for entry in page_data["page_daily_follows"]}
    fans_dict = {entry["end_time"][:10]: entry["value"] for entry in page_data["page_fans"]}
    impressions_dict = {entry["end_time"][:10]: entry["value"] for entry in page_data["page_impressions"]}
    impressions_unique_dict = {entry["end_time"][:10]: entry["value"] for entry in page_data["page_impressions_unique"]}
    views_total_dict = {entry["end_time"][:10]: entry["value"] for entry in page_data["page_views_total"]}

    complete_data = []
    total_follows = 0
    last_fans_value = 0
    total_impressions = 0
    total_impressions_unique = 0
    total_views_total = 0

    current_date = start_dt
    while current_date <= end_dt:
        date_str = current_date.strftime("%Y-%m-%d")
        follows = follows_dict.get(date_str, 0)
        fans = fans_dict.get(date_str, last_fans_value)
        impressions = impressions_dict.get(date_str, 0)
        impressions_unique = impressions_unique_dict.get(date_str, 0)
        views_total = views_total_dict.get(date_str, 0)

        total_follows += follows
        last_fans_value = fans
        total_impressions += impressions
        total_impressions_unique += impressions_unique
        total_views_total += views_total

        complete_data.append({
            "date": date_str,
            "follows": follows,
            "fans": fans,
            "impressions": impressions,
            "impressions_unique": impressions_unique,
            "views_total": views_total
        })

        current_date += timedelta(days=1)

    total_fans = last_fans_value

    return {
        "total_follows": total_follows,
        "total_fans": total_fans,
        "total_impressions": total_impressions,
        "total_impressions_unique": total_impressions_unique,
        "total_views_total": total_views_total,
        "data": complete_data
    }



def get_facebook_daily_insights(page_id, access_token, start_date, end_date):
    url = f"https://graph.facebook.com/{page_id}/insights"
    params = {
        "metric": "page_posts_impressions,page_posts_impressions_unique,page_posts_impressions_paid,page_post_engagements",
        "period": "day",
        "since": start_date,
        "until": end_date,
        "access_token": access_token
    }

    daily_insights = {
        "page_posts_impressions": [],
        "page_posts_impressions_unique": [],
        "page_posts_impressions_paid": [],
        "page_post_engagements": []  # Added engagement data
    }

    while url:
        response = requests.get(url, params=params if "?" not in url else {})
        if response.status_code == 200:
            json_response = response.json()
            for entry in json_response.get("data", []):
                metric_name = entry.get("name")
                if metric_name in daily_insights:
                    if entry.get("period") == "day":
                        daily_insights[metric_name].extend(entry.get("values", []))
            url = json_response.get("paging", {}).get("next")
        else:
            return False , {}
    return True , fill_missing_dates(daily_insights, start_date, end_date, page_id, access_token)



def get_daily_post_metrics(page_id, access_token, start_date, end_date):
    url = f"https://graph.facebook.com/{page_id}/published_posts"
    params = {
        "fields": "created_time,likes.summary(total_count),comments.summary(total_count),shares",
        "since": start_date,
        "until": end_date,
        "access_token": access_token
    }

    post_data = {}

    while url:
        response = requests.get(url, params=params if "?" not in url else {})
        if response.status_code == 200:
            json_response = response.json()
            for post in json_response.get("data", []):
                post_date = post["created_time"][:10]  
                likes = post.get("likes", {}).get("summary", {}).get("total_count", 0)
                comments = post.get("comments", {}).get("summary", {}).get("total_count", 0)
                shares = post.get("shares", {}).get("count", 0)

                if post_date not in post_data:
                    post_data[post_date] = {"posts": 0, "likes": 0, "comments": 0, "shares": 0}

                post_data[post_date]["posts"] += 1
                post_data[post_date]["likes"] += likes
                post_data[post_date]["comments"] += comments
                post_data[post_date]["shares"] += shares

            url = json_response.get("paging", {}).get("next")  
        else:
            return {"error": response.json()} 

    return post_data


def fill_missing_dates(daily_insights, start_date, end_date, page_id, access_token):

    start_dt = datetime.strptime(start_date, "%Y-%m-%d")
    end_dt = datetime.strptime(end_date, "%Y-%m-%d")

    post_metrics = get_daily_post_metrics(page_id, access_token, start_date, end_date)

    # Adding engagement data dictionary
    impressions_dict = {entry["end_time"][:10]: entry["value"] for entry in daily_insights["page_posts_impressions"]}
    unique_impressions_dict = {entry["end_time"][:10]: entry["value"] for entry in daily_insights["page_posts_impressions_unique"]}
    paid_impressions_dict = {entry["end_time"][:10]: entry["value"] for entry in daily_insights["page_posts_impressions_paid"]}
    engagements_dict = {entry["end_time"][:10]: entry["value"] for entry in daily_insights["page_post_engagements"]}  # Added engagement data

    complete_data = []
    total_impressions = 0
    total_unique_impressions = 0
    total_paid_impressions = 0
    total_posts = 0
    total_likes = 0
    total_comments = 0
    total_shares = 0
    total_engagements = 0  # For the total engagements

    current_date = start_dt
    while current_date <= end_dt:
        date_str = current_date.strftime("%Y-%m-%d")
        impressions = impressions_dict.get(date_str, 0)
        unique_impressions = unique_impressions_dict.get(date_str, 0)
        paid_impressions = paid_impressions_dict.get(date_str, 0)
        engagements = engagements_dict.get(date_str, 0)  # Added engagement value
        posts = post_metrics.get(date_str, {}).get("posts", 0)
        likes = post_metrics.get(date_str, {}).get("likes", 0)
        comments = post_metrics.get(date_str, {}).get("comments", 0)
        shares = post_metrics.get(date_str, {}).get("shares", 0)

        total_impressions += impressions
        total_unique_impressions += unique_impressions
        total_paid_impressions += paid_impressions
        total_posts += posts
        total_likes += likes
        total_comments += comments
        total_shares += shares
        total_engagements += engagements  # Adding engagements to total

        complete_data.append({
            "date": date_str,
            "impressions": impressions,
            "unique_impressions": unique_impressions,
            "paid_impressions": paid_impressions,
            "engagements": engagements,  # Added engagements to the data
            "posts": posts,
            "likes": likes,
            "comments": comments,
            "shares": shares,
            "interactions": likes + comments + shares
        })

        current_date += timedelta(days=1)

    return {
        "data": complete_data,
        "total_impressions": total_impressions,
        "total_unique_impressions": total_unique_impressions,
        "total_paid_impressions": total_paid_impressions,
        "total_posts": total_posts,
        "total_likes": total_likes,
        "total_comments": total_comments,
        "total_shares": total_shares,
        "total_engagements": total_engagements,  # Total engagements
        "total_interactions": total_likes + total_comments + total_shares
    }



def get_post_count(page_id, access_token, start_date, end_date):
    url = f"https://graph.facebook.com/{page_id}/published_posts"
    params = {
        "fields": "created_time,likes.summary(total_count),comments.summary(total_count),shares",
        "since": start_date,
        "until": end_date,
        "access_token": access_token
    }

    post_data = {"posts": 0}  # Initialize post count

    while url:
        response = requests.get(url, params=params if "?" not in url else None)
        if response.status_code == 200:
            json_response = response.json()
            for post in json_response.get("data", []):
                post_data["posts"] += 1  # Increment post count
            
            url = json_response.get("paging", {}).get("next")  # Get next page URL
        else:
            return {"error": response.json()} 

    return True, post_data["posts"]


def get_facebook_share_url(token,post_id):
    url = f"https://graph.facebook.com/v21.0/{post_id}?fields=permalink_url"

    headers = {
    'Authorization': f'Bearer {token}'
    }

    response = requests.request("GET", url, headers=headers)
    if response.status_code == 200:
        suffix = response.json()['permalink_url']
        return True,suffix
    else:
        return False , ''

# page_id = "300731683602563"
# access_token = "EAAVeFVzn8eUBO1RRv1Kxxpca0uwZAZCzKlvfXNrvP55UNhAssAZAlYVuZBZBf6W00jgeWlZAsTNc8WGyxWH6oaSqF92jRiIQpOhZC1k6yiP6vgHq7kTxsVFKAZAENz0f9weusGbWMZAWHomcpfyO3HDMwT2xyglg6hDrq77Kwiz8SvbToOZAbYCSL6qo8pQRR0SJmSs1KraOQI95jQZAIiDBivsGg6V"

# start_date = "2025-03-01"
# end_date = "2025-03-25"

# data = get_facebook_daily_insights(page_id, access_token, start_date, end_date)

# print(json.dumps(data, indent=4))


# page_id = "300731683602563"
# access_token = "EAAVeFVzn8eUBO1RRv1Kxxpca0uwZAZCzKlvfXNrvP55UNhAssAZAlYVuZBZBf6W00jgeWlZAsTNc8WGyxWH6oaSqF92jRiIQpOhZC1k6yiP6vgHq7kTxsVFKAZAENz0f9weusGbWMZAWHomcpfyO3HDMwT2xyglg6hDrq77Kwiz8SvbToOZAbYCSL6qo8pQRR0SJmSs1KraOQI95jQZAIiDBivsGg6V"
# start_date = "2024-12-30"
# end_date = "2025-03-23"

# data = get_facebook_page_metrics(page_id, access_token, start_date, end_date)

# print(json.dumps(data, indent=4))