import asyncio
import json
from datetime import datetime, timezone
from telethon import TelegramClient
from telethon.tl.types import (
    <PERSON>r, <PERSON><PERSON>, <PERSON>, MessageMediaPhoto, MessageMediaDocument,
    MessageMediaContact, MessageMediaGeo, MessageMediaVenue,
    MessageMediaWebPage, MessageMediaPoll, MessageMediaGame,
    MessageMediaInvoice, MessageMediaGeoLive, MessageMediaDice
)
from telethon.tl.functions.messages import GetHistoryRequest
from telethon.tl.functions.contacts import GetContactsRequest
from telethon.errors import FloodWaitError
import os

API_ID = 20715292
API_HASH = 'f8cd7e4a00102f0fed3304a2b4851293'

class TelegramChatHistory:
    def __init__(self, session_name='telegram_session'):
        self.api_id = API_ID
        self.api_hash = API_HASH
        session_dir = os.path.join(os.path.dirname(__file__), 'session')
        os.makedirs(session_dir, exist_ok=True)
        self.session_name = os.path.join(session_dir, session_name)
        self.client = None
    
    async def create_client(self):
        self.client = TelegramClient(self.session_name, self.api_id, self.api_hash)
        await self.client.connect()
        return self.client
    
    async def get_dialogs(self, limit=100):
        try:
            if not self.client:
                await self.create_client()
            
            if not await self.client.is_user_authorized():
                return {'success': False,'error': 'User not authenticated','message': 'Please authenticate first'}
            
            dialogs = await self.client.get_dialogs(limit=limit)
            
            dialog_list = []
            for dialog in dialogs:
                entity = dialog.entity
                
                if isinstance(entity, User):
                    entity_type = 'user'
                    name = f"{entity.first_name or ''} {entity.last_name or ''}".strip()
                    username = entity.username
                elif isinstance(entity, Chat):
                    entity_type = 'group'
                    name = entity.title
                    username = None
                elif isinstance(entity, Channel):
                    entity_type = 'channel' if entity.broadcast else 'supergroup'
                    name = entity.title
                    username = entity.username
                else:
                    continue
                
                dialog_info = {
                    'id': entity.id,
                    'name': name,
                    'username': username,
                    'type': entity_type,
                    'unread_count': dialog.unread_count,
                    'last_message_date': dialog.date.isoformat() if dialog.date else None,
                    'is_pinned': dialog.pinned
                }
                
                if dialog.message:
                    dialog_info['last_message'] = {
                        'text': dialog.message.message or '',
                        'date': dialog.message.date.isoformat() if dialog.message.date else None,
                        'from_id': getattr(dialog.message.from_id, 'user_id', None) if dialog.message.from_id else None
                    }
                
                dialog_list.append(dialog_info)
            
            return {'success': True,'dialogs': dialog_list,'count': len(dialog_list)}
            
        except Exception as e:
            return {'success': False,'error': str(e),'message': 'Failed to get dialogs'}
    
    async def get_chat_history(self, chat_id, limit=100, offset_id=0):
        try:
            if not self.client:
                await self.create_client()

            if not await self.client.is_user_authorized():
                return {'success': False,'error': 'User not authenticated','message': 'Please authenticate first'}

            entity = None
            try:
                entity = await self.client.get_entity(int(chat_id))
            except (ValueError, Exception):
                try:
                    entity = await self.client.get_entity(str(chat_id))
                except Exception:
                    try:
                        dialogs = await self.client.get_dialogs()
                        for dialog in dialogs:
                            if (hasattr(dialog.entity, 'id') and str(dialog.entity.id) == str(chat_id)) or \
                               (hasattr(dialog.entity, 'username') and dialog.entity.username == str(chat_id)):
                                entity = dialog.entity
                                break
                    except Exception:
                        pass

            if not entity:
                return {
                    'success': False,
                    'error': f'Cannot find entity with ID: {chat_id}',
                    'message': f'User/chat with ID {chat_id} not found in your contacts'}


            if limit is None or limit >= 10000:
                messages = await self.client.get_messages(
                    entity,
                    limit=None,
                    offset_id=offset_id
                )
            else:
                messages = await self.client.get_messages(
                    entity,
                    limit=limit,
                    offset_id=offset_id
                )

            message_list = []
            for message in messages:
                message_data = await self._format_message(message)
                message_list.append(message_data)

            return {
                'success': True,
                'chat_id': chat_id,
                'messages': message_list,
                'count': len(message_list),
                'has_more': len(messages) == limit
            }
            
        except Exception as e:
            return {'success': False,'error': str(e),'message': f'Failed to get chat history for {chat_id}'}
    
    async def _format_message(self, message):
        """Format a message object into a dictionary"""
        try:
            # Basic message info
            message_data = {
                'id': message.id,
                'date': message.date.isoformat() if message.date else None,
                'text': message.message or '',
                'from_id': getattr(message.from_id, 'user_id', None) if message.from_id else None,
                'to_id': getattr(message.peer_id, 'user_id', None) if hasattr(message.peer_id, 'user_id') else getattr(message.peer_id, 'chat_id', None),
                'is_outgoing': message.out,
                'reply_to_msg_id': message.reply_to.reply_to_msg_id if message.reply_to else None,
                'media_type': None,
                'media_info': None
            }
            
            if message.media:
                if isinstance(message.media, MessageMediaPhoto):
                    message_data['media_type'] = 'photo'
                    message_data['media_info'] = {
                        'photo_id': message.media.photo.id,
                        'has_spoiler': getattr(message.media, 'spoiler', False)
                    }
                
                elif isinstance(message.media, MessageMediaDocument):
                    doc = message.media.document
                    message_data['media_type'] = 'document'
                    message_data['media_info'] = {
                        'document_id': doc.id,
                        'file_name': next((attr.file_name for attr in doc.attributes if hasattr(attr, 'file_name')), None),
                        'mime_type': doc.mime_type,
                        'size': doc.size
                    }
                    
                    for attr in doc.attributes:
                        if hasattr(attr, 'duration'):  # Video or audio
                            if 'video' in doc.mime_type:
                                message_data['media_type'] = 'video'
                            elif 'audio' in doc.mime_type:
                                message_data['media_type'] = 'audio'
                        elif hasattr(attr, 'alt'):  # Sticker
                            message_data['media_type'] = 'sticker'
                            message_data['media_info']['alt'] = attr.alt
                
                elif isinstance(message.media, MessageMediaContact):
                    message_data['media_type'] = 'contact'
                    message_data['media_info'] = {
                        'phone_number': message.media.phone_number,
                        'first_name': message.media.first_name,
                        'last_name': message.media.last_name
                    }
                
                elif isinstance(message.media, MessageMediaGeo):
                    message_data['media_type'] = 'location'
                    message_data['media_info'] = {
                        'latitude': message.media.geo.lat,
                        'longitude': message.media.geo.long
                    }
                
                elif isinstance(message.media, MessageMediaWebPage):
                    message_data['media_type'] = 'webpage'
                    webpage = message.media.webpage
                    message_data['media_info'] = {
                        'url': webpage.url,
                        'title': webpage.title,
                        'description': webpage.description
                    }
                
                elif isinstance(message.media, MessageMediaPoll):
                    message_data['media_type'] = 'poll'
                    poll = message.media.poll
                    message_data['media_info'] = {
                        'question': poll.question,
                        'answers': [answer.text for answer in poll.answers]
                    }
            
            return message_data
            
        except Exception as e:
            return {
                'id': message.id,
                'date': message.date.isoformat() if message.date else None,
                'text': message.message or '',
                'from_id': getattr(message.from_id, 'user_id', None) if message.from_id else None,
                'error': f'Failed to format message: {str(e)}'
            }
    

    
    async def send_message(self, chat_id, message_text, file_path=None):
        try:
            if not self.client:
                await self.create_client()

            if not await self.client.is_user_authorized():
                return {
                    'success': False,
                    'error': 'User not authenticated',
                    'message': 'Please authenticate first'
                }

            entity = None
            try:
                entity = await self.client.get_entity(int(chat_id))
            except (ValueError, Exception):
                try:
                    entity = await self.client.get_entity(str(chat_id))
                except Exception:
                    try:
                        dialogs = await self.client.get_dialogs()
                        for dialog in dialogs:
                            if (hasattr(dialog.entity, 'id') and str(dialog.entity.id) == str(chat_id)) or \
                               (hasattr(dialog.entity, 'username') and dialog.entity.username == str(chat_id)):
                                entity = dialog.entity
                                break
                    except Exception:
                        pass

            if not entity:
                return {
                    'success': False,
                    'error': f'Cannot find entity with ID: {chat_id}',
                    'message': f'User/chat with ID {chat_id} not found in your contacts'
                }

            if file_path:
                # Send message with file
                sent_message = await self.client.send_file(
                    entity,
                    file_path,
                    caption=message_text if message_text else None
                )
            else:
                # Send text message only
                sent_message = await self.client.send_message(entity, message_text)

            # Format the sent message
            message_data = await self._format_message(sent_message)

            return {
                'success': True,
                'message': 'Message sent successfully',
                'sent_message': message_data
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': f'Failed to send message to chat {chat_id}'
            }

    async def get_user_from_dialogs(self, user_id):
        try:
            if not self.client:
                await self.create_client()

            if not await self.client.is_user_authorized():
                return None

            dialogs = await self.client.get_dialogs()
            for dialog in dialogs:
                if hasattr(dialog.entity, 'id') and str(dialog.entity.id) == str(user_id):
                    return dialog.entity
            return None
        except Exception:
            return None

    async def disconnect(self):
        if self.client:
            await self.client.disconnect()

# Synchronous wrapper functions for Django views
def get_telegram_dialogs(limit=None, session_name='telegram_session'):
    chat_history = TelegramChatHistory(session_name)
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        result = loop.run_until_complete(chat_history.get_dialogs(limit))
        loop.run_until_complete(chat_history.disconnect())
        return result
    finally:
        loop.close()



def send_telegram_message(chat_id, message_text, file_path=None, session_name='telegram_session'):
    chat_history = TelegramChatHistory(session_name)
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        result = loop.run_until_complete(chat_history.send_message(chat_id, message_text, file_path))
        loop.run_until_complete(chat_history.disconnect())
        return result
    finally:
        loop.close()

def get_telegram_user_chats(user_id, limit=None, offset_id=0, session_name='telegram_session'):
    chat_history = TelegramChatHistory(session_name)
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        user_entity = loop.run_until_complete(chat_history.get_user_from_dialogs(user_id))
        if not user_entity:
            return {
                'success': False,
                'error': f'User with ID {user_id} not found in your dialogs',
                'message': f'Cannot find user {user_id} in your Telegram contacts'
            }

        # Now get ALL chat history using the entity (limit=None gets all messages)
        result = loop.run_until_complete(chat_history.get_chat_history(user_entity.id, limit, offset_id))
        loop.run_until_complete(chat_history.disconnect())
        return result
    finally:
        loop.close()


