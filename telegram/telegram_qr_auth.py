import asyncio
import os
import time
import base64
from telethon import TelegramClient
from telethon.tl.functions.auth import ExportLoginTokenRequest, ImportLoginTokenRequest
from telethon.errors import SessionPasswordNeededError, AuthKeyUnregisteredError
from helpers.generate_qr import generate_qr_code
import json

# Telegram API credentials
API_ID = 20715292
API_HASH = 'f8cd7e4a00102f0fed3304a2b4851293'

class TelegramQRAuth:
    def __init__(self, session_name='telegram_qr_session'):
        self.api_id = API_ID
        self.api_hash = API_HASH
        
        session_dir = os.path.join(os.path.dirname(__file__), 'session')
        os.makedirs(session_dir, exist_ok=True)
        self.session_name = os.path.join(session_dir, session_name)
        self.client = None
        self.login_token = None
        self.qr_timeout = 300  # 5 minutes timeout for QR code
        
    async def create_client(self):
        """Create and connect Telegram client"""
        self.client = TelegramClient(self.session_name, self.api_id, self.api_hash)
        await self.client.connect()
        return self.client
    
    async def generate_qr_login_token(self):
        """Generate QR login token and return QR code data"""
        try:
            if not self.client:
                await self.create_client()
            
            # Request login token for QR code
            result = await self.client(ExportLoginTokenRequest(
                api_id=self.api_id,
                api_hash=self.api_hash,
                except_ids=[]
            ))
            
            self.login_token = result.token
            
            # Create QR code URL format: tg://login?token=<base64_token>
            token_b64 = base64.urlsafe_b64encode(self.login_token).decode().rstrip('=')
            qr_url = f"tg://login?token={token_b64}"
            
            # Generate QR code image
            timestamp = int(time.time())
            qr_filename = f"telegram_qr_{timestamp}"
            qr_path = generate_qr_code(qr_url, qr_filename)
            
            return {
                'success': True,
                'qr_url': qr_url,
                'qr_image_path': qr_path,
                'token': token_b64,
                'expires_at': timestamp + self.qr_timeout,
                'message': 'QR code generated successfully'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to generate QR login token'
            }
    
    async def check_qr_login_status(self, token_b64):
        """Check if QR code has been scanned and accepted"""
        try:
            if not self.client:
                await self.create_client()
            
            # Decode token back to bytes
            token_bytes = base64.urlsafe_b64decode(token_b64 + '==')
            
            try:
                # Try to import the login token
                result = await self.client(ImportLoginTokenRequest(token=token_bytes))
                
                if hasattr(result, 'authorization'):
                    # Successfully logged in
                    me = await self.client.get_me()
                    
                    return {
                        'success': True,
                        'status': 'completed',
                        'user_info': {
                            'user_id': me.id,
                            'username': me.username,
                            'first_name': me.first_name,
                            'last_name': me.last_name,
                            'phone': me.phone
                        },
                        'message': 'QR login completed successfully'
                    }
                else:
                    # Still waiting for user to scan/accept
                    return {
                        'success': True,
                        'status': 'waiting',
                        'message': 'Waiting for QR code to be scanned'
                    }
                    
            except AuthKeyUnregisteredError:
                return {
                    'success': True,
                    'status': 'waiting',
                    'message': 'Waiting for QR code to be scanned'
                }
            except Exception as import_error:
                # Check if it's a timeout or other error
                if 'TIMEOUT' in str(import_error).upper():
                    return {
                        'success': False,
                        'status': 'expired',
                        'message': 'QR code has expired'
                    }
                else:
                    return {
                        'success': False,
                        'status': 'error',
                        'error': str(import_error),
                        'message': 'Error checking QR login status'
                    }
                    
        except Exception as e:
            return {
                'success': False,
                'status': 'error',
                'error': str(e),
                'message': 'Failed to check QR login status'
            }
    
    async def complete_qr_authentication(self, token_b64):
        """Complete the QR authentication process"""
        try:
            if not self.client:
                await self.create_client()
            
            # Check status first
            status_result = await self.check_qr_login_status(token_b64)
            
            if status_result['success'] and status_result['status'] == 'completed':
                return status_result
            else:
                return {
                    'success': False,
                    'message': 'QR authentication not completed yet',
                    'status': status_result.get('status', 'unknown')
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to complete QR authentication'
            }
    
    async def disconnect(self):
        """Disconnect the client"""
        if self.client:
            await self.client.disconnect()


# Synchronous wrapper functions
def generate_telegram_qr_token(session_name='telegram_qr_session'):
    """Generate QR login token synchronously"""
    auth = TelegramQRAuth(session_name)
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        result = loop.run_until_complete(auth.generate_qr_login_token())
        loop.run_until_complete(auth.disconnect())
        return result
    finally:
        loop.close()

def check_telegram_qr_status(token_b64, session_name='telegram_qr_session'):
    """Check QR login status synchronously"""
    auth = TelegramQRAuth(session_name)
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        result = loop.run_until_complete(auth.check_qr_login_status(token_b64))
        loop.run_until_complete(auth.disconnect())
        return result
    finally:
        loop.close()

def complete_telegram_qr_auth(token_b64, session_name='telegram_qr_session'):
    """Complete QR authentication synchronously"""
    auth = TelegramQRAuth(session_name)
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        result = loop.run_until_complete(auth.complete_qr_authentication(token_b64))
        loop.run_until_complete(auth.disconnect())
        return result
    finally:
        loop.close()
