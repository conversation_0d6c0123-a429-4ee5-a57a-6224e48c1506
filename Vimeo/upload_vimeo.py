from vimeo import VimeoClient
# client = VimeoClient(token='e3101b8c5cac815d18fef3a0e98b8af1')

def upload_vimeo_video(token,file_path,title,description):
    client = VimeoClient(token=token)
    try:
        video_uri = client.upload(file_path, data={'name':title, 'description': description})
        if video_uri:
            return True , video_uri
        else:
            return False
    except Exception as e:
        return False
        exit(1)
